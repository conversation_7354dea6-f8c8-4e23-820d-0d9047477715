# OpenAI GPT-5-nano with Native Web Search Demo

A demonstration of OpenAI's GPT-5-nano model with native web search capabilities using Vercel AI SDK v5 and AI Elements components.

## Overview

This demo showcases:
- **OpenAI GPT-5-nano**: Latest reasoning model with native web search
- **Native Web Search**: OpenAI's built-in `web_search_preview` tool
- **Real-time Streaming**: Live display of reasoning steps and search results
- **AI Elements**: Proper use of Vercel AI SDK v5 UI components
- **Citations & Sources**: Proper display of search sources and quotations

## Features

### Model Configuration
- **Model**: `gpt-5-nano` via OpenAI Responses API
- **Reasoning Effort**: Medium (configurable: minimal, low, medium, high)
- **Search Context**: Medium (configurable: low, medium, high)
- **Text Verbosity**: Medium (configurable: low, medium, high)
- **Reasoning Summary**: Auto (condensed reasoning display)

### Frontend Features
- **Streaming UI**: Real-time display of reasoning steps
- **Tool Call Display**: Visual representation of web search calls
- **Search Results**: Proper citation and source display
- **Error Handling**: Graceful error handling and user feedback
- **Responsive Design**: Mobile-friendly interface

### Backend Features
- **Native Web Search**: Uses OpenAI's `web_search_preview` tool
- **Multi-step Tool Calling**: Supports complex search sequences
- **Response Logging**: Complete response structure logging
- **Error Handling**: Robust error handling and logging

## Architecture

### Route Structure
```
/chat-with-search-demo/
├── page.tsx              # Main chat interface
└── README.md             # This documentation

/api/chat-with-search-demo/
└── route.ts              # API endpoint with OpenAI integration
```

### Key Components

#### Frontend (`page.tsx`)
- Uses Vercel AI Elements components:
  - `Conversation` & `ConversationContent` for chat container
  - `Message` & `MessageContent` for message display
  - `Tool`, `ToolHeader`, `ToolContent` for tool call rendering
  - `Reasoning`, `ReasoningTrigger`, `ReasoningContent` for reasoning display
  - `Response` for AI response rendering
  - `PromptInput` components for user input

#### Backend (`route.ts`)
- OpenAI GPT-5-nano model configuration
- Native web search tool setup
- Streaming response handling
- Debug logging and response structure capture

## Usage

### Starting a Chat
1. Navigate to `/chat-with-search-demo`
2. Click "Start Chat" for a preset query
3. Or type your own question requiring current information

### Example Queries
- "What are the latest developments in AI technology this week?"
- "What happened in the stock market today?"
- "Current weather conditions in major cities"
- "Recent news about climate change initiatives"

## Technical Implementation

### OpenAI Configuration
```typescript
const result = streamText({
  model: openai.responses('gpt-5-nano'),
  tools: {
    web_search_preview: openai.tools.webSearchPreview({
      searchContextSize: 'medium',
    }),
  },
  toolChoice: { type: 'tool', toolName: 'web_search_preview' },
  providerOptions: {
    openai: {
      reasoningEffort: 'medium',
      reasoningSummary: 'auto',
      textVerbosity: 'medium',
    },
  },
})
```

### AI Elements Integration
```typescript
// Tool call rendering
<Tool key={`${message.id}-tool-${index}`}>
  <ToolHeader type={maybeTool.type} state={maybeTool.state} />
  <ToolContent>
    <ToolInput input={maybeTool.input} />
    <ToolOutput output={maybeTool.output} />
  </ToolContent>
</Tool>

// Reasoning display
<Reasoning isStreaming={isStreaming} defaultOpen={false}>
  <ReasoningTrigger />
  <ReasoningContent>{part.text}</ReasoningContent>
</Reasoning>
```

## Environment Requirements

### Required Environment Variables
- `OPENAI_API_KEY`: OpenAI API key for GPT-5-nano access

### Optional Configuration
- Search context size: Configurable in API route
- Reasoning effort level: Adjustable based on use case
- Text verbosity: Can be modified for different response lengths

## Development Notes

### Debugging
- Complete response structures are logged to console
- Response metadata includes reasoning tokens and cache hits
- Tool call parameters and results are fully logged

### Performance Considerations
- Reasoning models have higher latency due to reasoning phase
- Web search adds additional latency for real-time information
- Streaming provides better user experience during processing

### Future Enhancements
- Sample data export functionality (currently commented out)
- Custom search location configuration
- Advanced tool choice strategies
- Response caching for similar queries

## Related Documentation

- [OpenAI GPT-5 Documentation](https://platform.openai.com/docs/models/gpt-5)
- [Vercel AI SDK v5](https://ai-sdk.dev/)
- [AI Elements Components](https://ai-sdk.dev/docs/ai-elements)
- [OpenAI Web Search Tool](https://platform.openai.com/docs/api-reference/chat/create#chat-create-tools)

## Testing

The implementation passes all existing tests and builds successfully:
- ✅ TypeScript compilation
- ✅ ESLint validation  
- ✅ Jest test suite
- ✅ Next.js build process

## Comparison with Existing Chat Demo

| Feature | chat-demo | chat-with-search-demo |
|---------|-----------|----------------------|
| Model | Azure OpenAI gpt-4.1-mini | OpenAI GPT-5-nano |
| Search | Brave Search API | Native OpenAI web search |
| Reasoning | Basic responses | Advanced reasoning display |
| Tool Integration | Custom tools | Native OpenAI tools |
| Citations | Manual formatting | Built-in citation support |
