'use client'

import { useState, useRef, useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport, generateId } from 'ai'
import { FiSearch } from 'react-icons/fi'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Too<PERSON>,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
} from '@/components/ai-elements/prompt-input'

export default function ChatWithSearchDemoPage() {
  // Local input with explicit type annotation for clarity
  const [input, setInput] = useState<string>('')
  // Generate a stable chat ID that won't cause hydration mismatch
  const [chatId, setChatId] = useState<string>('')
  // Track a pending outbound text when the transport receives an empty messages array
  const pendingTextRef = useRef<string | null>(null)

  // Initialize chat ID on client side to avoid hydration mismatch
  useEffect(() => {
    setChatId(`chat-search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
  }, [])

  const { messages, sendMessage, status, error } = useChat({
    id: chatId,
    transport: new DefaultChatTransport({
      api: '/api/dragTree/chat-with-search-demo',
      // Send only the last message to the server (AI SDK v5 persistence pattern)
      prepareSendMessagesRequest({ messages, id: transportId }) {
        const lastMessage = messages[messages.length - 1]

        // If no last message, fall back to traditional pattern
        if (!lastMessage) {
          console.log(
            '🔄 [Chat Search Demo] No last message, using traditional pattern'
          )
          // If we have a pending text (e.g., from Start Chat), synthesize a UIMessage
          if (pendingTextRef.current) {
            const synthesized = {
              id: generateId(),
              role: 'user' as const,
              parts: [{ type: 'text' as const, text: pendingTextRef.current }],
            }
            // Clear pending text once consumed
            pendingTextRef.current = null

            return {
              body: {
                message: synthesized,
                id: transportId,
                context: { chatId, app: 'chat-with-search-demo' },
              },
            }
          }

          return {
            body: {
              messages,
              chatId: transportId,
              context: { chatId, app: 'chat-with-search-demo' },
            },
          }
        }

        console.log(
          '🔄 [Chat Search Demo] Sending last message only:',
          lastMessage
        )
        return {
          body: {
            message: lastMessage,
            id: transportId,
            context: { chatId, app: 'chat-with-search-demo' },
          },
        }
      },
    }),
    experimental_throttle: 200,
    onError: error => {
      console.error('Chat error:', error)
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && status !== 'streaming') {
      // Set pending text before triggering sendMessage so transport can synthesize if needed
      pendingTextRef.current = input
      sendMessage({ text: input })
      setInput('')
    }
  }

  // Set example text in input
  const handleUseExample = () => {
    setInput('What are the latest developments in AI technology this week?')
  }

  return (
    <div className="flex h-screen flex-col bg-white">
      {/* Header - Compact style matching AI Pane */}
      <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FiSearch className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              OpenAI GPT-5-nano with Native Web Search
            </h1>
            <p className="text-sm text-gray-600">
              Real-time web search with reasoning steps and citations
            </p>
          </div>
        </div>
      </div>

      {/* Context Display - Matching AI Pane Chat style */}
      <div className="px-4 py-2 border-b border-gray-100 bg-white">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <span>Using OpenAI GPT-5-nano with native web search tool</span>
          {chatId && (
            <span className="text-gray-400">Chat ID: {chatId.slice(-8)}</span>
          )}
        </div>
      </div>

      {/* Chat Container */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Conversation className="flex-1">
          <ConversationContent>
            {messages.length === 0 && (
              <div className="flex h-full items-center justify-center">
                <div className="text-center max-w-md">
                  <FiSearch className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <h2 className="text-lg font-medium text-gray-500 mb-4">
                    AI Web Search Demo
                  </h2>
                  <p className="text-sm text-gray-400 mb-6">
                    Experience OpenAI's native web search with reasoning steps
                  </p>
                  <button
                    onClick={handleUseExample}
                    className="px-4 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                  >
                    Use Example Query
                  </button>
                  <p className="text-xs text-gray-400 mt-3">
                    Or type your own question below
                  </p>
                </div>
              </div>
            )}

            {messages.map(message => (
              <Message key={message.id} from={message.role}>
                <MessageContent>
                  {/* Render message parts */}
                  {message.parts.map((part, index) => {
                    if (part.type === 'text') {
                      return (
                        <Response
                          key={`${message.id}-text-${index}`}
                          defaultOrigin="https://localhost:3000"
                        >
                          {part.text}
                        </Response>
                      )
                    }

                    if (part.type === 'reasoning') {
                      return (
                        <Reasoning
                          key={`${message.id}-reasoning-${index}`}
                          isStreaming={
                            status === 'streaming' &&
                            message.id === messages[messages.length - 1]?.id
                          }
                          defaultOpen={false}
                        >
                          <ReasoningTrigger />
                          <ReasoningContent>{part.text}</ReasoningContent>
                        </Reasoning>
                      )
                    }

                    // Handle tool UI parts if present
                    const maybeTool = part as any
                    if (
                      maybeTool &&
                      typeof maybeTool === 'object' &&
                      'state' in maybeTool &&
                      'type' in maybeTool &&
                      (('input' in maybeTool &&
                        maybeTool.input !== undefined) ||
                        ('output' in maybeTool &&
                          maybeTool.output !== undefined) ||
                        ('errorText' in maybeTool && maybeTool.errorText))
                    ) {
                      return (
                        <Tool key={`${message.id}-tool-${index}`}>
                          <ToolHeader
                            type={maybeTool.type}
                            state={maybeTool.state}
                          />
                          <ToolContent>
                            {'input' in maybeTool && (
                              <ToolInput input={maybeTool.input} />
                            )}
                            <ToolOutput
                              output={
                                'output' in maybeTool ? (
                                  typeof maybeTool.output === 'string' ? (
                                    maybeTool.output
                                  ) : (
                                    <pre className="text-xs">
                                      {JSON.stringify(
                                        maybeTool.output,
                                        null,
                                        2
                                      )}
                                    </pre>
                                  )
                                ) : undefined
                              }
                              errorText={
                                'errorText' in maybeTool
                                  ? maybeTool.errorText
                                  : undefined
                              }
                            />
                          </ToolContent>
                        </Tool>
                      )
                    }

                    return null
                  })}
                </MessageContent>
              </Message>
            ))}

            {error && (
              <div className="mx-4 rounded-lg border border-red-200 bg-red-50 p-4">
                <p className="text-sm text-red-600">Error: {error.message}</p>
              </div>
            )}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        {/* Input Area - Matching AI Pane Chat styling */}
        <div className="border-t border-gray-200 bg-white p-4">
          <div className="max-w-4xl mx-auto">
            <PromptInput
              onSubmit={handleSubmit}
              className="relative flex items-end bg-gray-50 rounded-xl border border-gray-200 focus-within:border-green-500 transition-colors"
            >
              <PromptInputTextarea
                value={input}
                onChange={e => setInput(e.target.value)}
                placeholder="Ask me anything requiring current information... (e.g., latest AI developments, current events, stock prices)"
                className="flex-1 min-h-[44px] max-h-32 resize-none border-0 bg-transparent px-4 py-3 text-sm placeholder:text-gray-500 focus:outline-none focus:ring-0"
                disabled={status === 'streaming'}
              />
              <PromptInputSubmit
                status={status === 'streaming' ? 'streaming' : 'ready'}
                disabled={!input.trim() || status === 'streaming'}
                className="m-2 h-8 w-8 p-0 rounded-lg bg-green-600 hover:bg-green-700 disabled:bg-gray-300 transition-colors"
              />
            </PromptInput>

            {/* Helpful hints - matching AI Pane Chat */}
            <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
              <span>Press Enter to send, Shift+Enter for new line</span>
              <span>{input.length} characters</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
