import {
  streamText,
  UIMessage,
  convertToModelMessages,
  stepCountIs,
} from 'ai'
import { openai } from '@ai-sdk/openai'
import { NextRequest } from 'next/server'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

/**
 * Chat with Search Demo API Route
 * 
 * Demonstrates OpenAI GPT-5-nano with native web search capabilities
 * using Vercel AI SDK v5 and OpenAI's web_search_preview tool.
 * 
 * Features:
 * - OpenAI GPT-5-nano model with medium reasoning effort
 * - Native web search tool with medium search context
 * - Real-time streaming of reasoning steps and search results
 * - Complete response logging and sample data export
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log('🚀 [Chat Search Demo] Request received:', {
      hasMessage: !!body.message,
      hasMessages: !!body.messages,
      context: body.context,
    })

    // Extract message from request (AI SDK v5 pattern)
    let messages: UIMessage[] = []
    
    if (body.message) {
      // Single message pattern (preferred for AI SDK v5)
      messages = [body.message]
      console.log('📝 [Chat Search Demo] Processing single message:', body.message.parts?.[0]?.text?.slice(0, 100))
    } else if (body.messages) {
      // Fallback to messages array
      messages = body.messages
      console.log('📝 [Chat Search Demo] Processing messages array:', messages.length)
    } else {
      throw new Error('No message or messages provided')
    }

    // Convert to model messages format
    const modelMessages = convertToModelMessages(messages)
    console.log('🔄 [Chat Search Demo] Converted to model messages:', modelMessages.length)

    // Create the streaming response with OpenAI GPT-5-nano and native web search
    const result = streamText({
      model: openai.responses('gpt-5-nano'), // Use responses API for web search support
      messages: modelMessages,
      tools: {
        // OpenAI's native web search tool
        web_search_preview: openai.tools.webSearchPreview({
          // Configure search context and reasoning
          searchContextSize: 'medium', // 'low', 'medium', or 'high'
          // Optional: specify user location for better search results
          // userLocation: {
          //   type: 'approximate',
          //   city: 'San Francisco',
          //   region: 'California',
          // },
        }),
      },
      // Force web search tool usage for demonstration
      toolChoice: { type: 'tool', toolName: 'web_search_preview' },
      maxOutputTokens: 4000,
      temperature: 0.7,
      // Enable multi-step tool calling for complex searches
      stopWhen: stepCountIs(10),
      // Configure reasoning effort and summaries
      providerOptions: {
        openai: {
          reasoningEffort: 'medium', // 'minimal', 'low', 'medium', 'high'
          reasoningSummary: 'auto', // 'auto' for condensed or 'detailed' for comprehensive
          textVerbosity: 'medium', // 'low', 'medium', 'high'
          // Enable response storage for debugging
          store: true,
          metadata: {
            demo: 'chat-with-search',
            timestamp: new Date().toISOString(),
          },
        },
      },
      system: `You are a helpful AI assistant with access to real-time web search capabilities. 

When users ask questions, you should:
1. Use web search to find current, accurate information
2. Provide comprehensive answers based on your search results
3. Include proper citations and sources
4. Show your reasoning process when helpful

Always search for the most recent and relevant information to provide accurate, up-to-date responses.`,
    })

    console.log('🌊 [Chat Search Demo] Starting stream response')
    
    // Note: For debugging and sample data export, we'll handle this in a separate endpoint
    // or through response inspection in the frontend, as the current AI SDK version
    // doesn't support onFinish callback in the same way.
    
    return result.toTextStreamResponse()

  } catch (error) {
    console.error('💥 [Chat Search Demo] API Error:', error)
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}
